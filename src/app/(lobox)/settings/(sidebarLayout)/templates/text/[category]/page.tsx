'use client';

import { useRouter } from 'next/navigation';
import React from 'react';
import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import TemplateList from '@shared/components/Organism/AutomationModal/components/TemplateForm/TemplateList';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import {
  isValidTemplateCategoryType,
  type TemplateCategoryType,
} from '@shared/utils/api/template';
import { routeNames } from '@shared/utils/constants';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useCategoryTemplateActions } from './hooks/useCategoryTemplateActions';
import { useCategoryTemplateList } from './hooks/useCategoryTemplateList';
import type {
  NormalizedTemplate,
  TemplateAction,
} from '@shared/components/Organism/AutomationModal/types/template.types';

function getPageDataFromParams(category: TemplateCategoryType) {
  const pageData = {
    rejection: 'rejection_templates',
    message: 'message_templates',
    meeting: 'meeting_templates',
    email: 'email_templates',
  };

  const checkingHasDefault = {
    email: false,
    message: false,
    rejection: true,
    meeting: true,
  };

  return {
    title: pageData[category],
    hasDefault: checkingHasDefault[category],
  };
}

export default function Page({ params }: { params: { category: string } }) {
  const router = useRouter();
  const { t } = useTranslation();
  const category = params?.category;

  const templateList = useCategoryTemplateList({
    category: category as TemplateCategoryType,
    searchEnabled: true,
  });

  const templateActions = useCategoryTemplateActions(
    {
      category: category as TemplateCategoryType,
      onTemplateCreated: () => {
        templateList.refetch();
      },
      onTemplateUpdated: () => {
        templateList.refetch();
      },
      onTemplateDeleted: () => {
        templateList.refetch();
      },
      onDefaultChanged: () => {
        templateList.refetch();
      },
    },
    {
      message: t('template_duplicated_message'),
      title: t('template_duplicated_title'),
    }
  );

  if (!isValidTemplateCategoryType(category)) {
    router.replace('/404');

    return null;
  }

  const handleEdit = (id?: string) => {
    router.push(routeNames.settingsTextTemplates.makeCrEditRoute(category, id));
  };

  const templateActionsList: TemplateAction[] = [
    {
      id: 'duplicate',
      icon: 'duplicate',
      title: t('duplicate'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.duplicateTemplate(template);
      },
    },
    {
      id: 'edit',
      icon: 'pen',
      title: t('edit'),
      onClick: (template: NormalizedTemplate) => {
        handleEdit(template?.id);
      },
    },
    {
      id: 'delete',
      icon: 'trash',
      title: t('delete'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.deleteTemplate(template);
      },
    },
  ];

  const handleCreate = () => {
    router.push(routeNames.settingsTextTemplates.makeCrEditRoute(category));
  };

  const handleSetDefault = (
    templateId: string,
    isCurrentlyDefault: boolean,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    templateActions.setDefaultTemplate(templateId, isCurrentlyDefault);
  };

  const { title, hasDefault } = getPageDataFromParams(category);

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeSettingsScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Container
        title={t(title)}
        contentWrapperClassName="!p-0 !h-full"
        contentWrapperClassNameDesktop="!p-0 !h-full"
      >
        <Flex className="p-20 h-full">
          <TemplateList
            templates={templateList.templates}
            isLoading={templateList.isLoading}
            searchQuery={templateList.searchQuery}
            onSearchChange={templateList.handleSearchChange}
            onTemplateClick={handleEdit}
            onSetDefault={handleSetDefault}
            actions={templateActionsList}
            isUpdatingDefault={templateActions.isUpdatingDefault}
            config={{
              showSearch: true,
              showActions: true,
              showDefaultToggle: hasDefault,
            }}
          />
        </Flex>

        <Flex className="p-20 !border-t !border-t-techGray_10 !border-solid mt-auto">
          <Button
            fullWidth
            label={t('create_template')}
            leftIcon="plus"
            leftType="fas"
            schema="semi-transparent"
            variant="default"
            onClick={handleCreate}
          />
        </Flex>
      </Container>
    </PermissionsGate>
  );
}
