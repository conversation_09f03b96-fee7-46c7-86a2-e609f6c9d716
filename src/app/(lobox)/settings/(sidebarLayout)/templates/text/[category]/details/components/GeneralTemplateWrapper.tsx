'use client';

import React from 'react';
import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import useCreateEditTemplateForm from '@shared/components/Organism/TemplateForm/useCreateEditTemplateForm';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import useTranslation from '@shared/utils/hooks/useTranslation';

interface GeneralTemplateWrapperProps {
  category: string;
  entityId: string;
}

const GeneralTemplateWrapper: React.FC<GeneralTemplateWrapperProps> = ({
  category,
  entityId,
}) => {
  const { t } = useTranslation();

  const {
    groups,
    title,
    buttonTitle,
    transform,
    initialData,
    onCancel,
    onSuccess,
    apiFunc,
    isLoading,
  } = useCreateEditTemplateForm(category, entityId);

  return (
    <Container
      title={t(title)}
      className="!h-full"
      contentWrapperClassName="!p-0 !h-full"
      contentWrapperClassNameDesktop="!p-0 !h-full"
    >
      {isLoading ? null : (
        <Form
          initialValues={initialData}
          transform={transform}
          apiFunc={apiFunc}
          onSuccess={onSuccess}
          className="h-full"
        >
          {(props) => (
            <Flex className="justify-between flex-1 h-full">
              <Flex className="p-20 flex-1">
                <Flex className="w-full !rounded-xl !bg-gray_5 p-20 ">
                  <DynamicFormBuilder groups={groups(props)} />
                </Flex>
              </Flex>
              <Flex className="mt-12 !border-t p-20 !border-t-techGray_10 !border-solid !flex-row gap-8">
                <Button
                  fullWidth
                  label={t('discard')}
                  schema="gray-semi-transparent"
                  onClick={onCancel(props.dirty)}
                />
                <SubmitButton
                  fullWidth
                  schema="primary-blue"
                  labelFont="bold"
                  label={t(buttonTitle)}
                />
              </Flex>
            </Flex>
          )}
        </Form>
      )}
    </Container>
  );
};

export default GeneralTemplateWrapper;
